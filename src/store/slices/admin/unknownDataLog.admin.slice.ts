import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
} from "@reduxjs/toolkit";
import { unknownDataAdminApi } from "../../../services/mainApi/admin/unknownData.admin.mainApi";
import {
  AdminUnknownDataEntry,
  AdminUnknownDataListParams,
} from "../../../services/mainApi/admin/types/unknownData.admin.mainApi.types";
import { RootState } from "../../../store";
import dayjs from "dayjs";

interface UnknownDataPaginationMeta {
  timestamp: string;
  local_time: string;
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

interface State {
  unknownDataLogs: AdminUnknownDataEntry[];
  loading: boolean;
  error: string | null;

  filter: {
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    page: number;
    limit: number;
    orderBy: string;
  };
  pagination: UnknownDataPaginationMeta;
}

const initialState: State = {
  unknownDataLogs: [],
  loading: false,
  error: null,

  filter: {
    startDate: dayjs().subtract(1, 'day').startOf('day').format('YYYY-MM-DD'),
    endDate: dayjs().endOf('day').format('YYYY-MM-DD'),
    startTime: "00:00",
    endTime: "23:59",
    page: 1,
    limit: 10,
    orderBy: "original_submitted_time",
  },
  pagination: {
    timestamp: "",
    local_time: "",
    total: 0,
    page: 1,
    limit: 10,
    total_pages: 0,
  },
};

export const fetchUnknownDataLogs = createAsyncThunk(
  "unknownDataLogAdmin/fetchUnknownDataLogs",
  async (
    {
      branchCode,
    }: {
      branchCode: string;
    },
    { getState }
  ) => {
    const state = getState() as RootState;
    const unknownDataLogAdmin = state.unknownDataLogAdmin;

    const params: AdminUnknownDataListParams = {
      page: unknownDataLogAdmin.filter.page,
      limit: unknownDataLogAdmin.filter.limit,
      start_date:
        unknownDataLogAdmin.filter.startDate || undefined,
      end_date:
        unknownDataLogAdmin.filter.endDate || undefined,
      start_time:
        unknownDataLogAdmin.filter.startTime || undefined,
      end_time:
        unknownDataLogAdmin.filter.endTime || undefined,
      order_by: unknownDataLogAdmin.filter.orderBy,
    };

    return await unknownDataAdminApi.getUnknownData(
      branchCode,
      params
    );
  }
);

const unknownDataLogAdminSlice = createSlice({
  name: "unknownDataLogAdmin",
  initialState,
  reducers: {
    setFilter: (
      state,
      action: PayloadAction<Partial<State["filter"]>>
    ) => {
      state.filter = { ...state.filter, ...action.payload };
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.filter.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.filter.limit = action.payload;
    },
    setOrderBy: (state, action: PayloadAction<string>) => {
      state.filter.orderBy = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        ...initialState.filter,
        page: state.filter.page,
        limit: state.filter.limit,
      };
    },
    setStartDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startDate = action.payload;
    },
    setEndDate: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endDate = action.payload;
    },
    setStartTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.startTime = action.payload;
    },
    setEndTime: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.filter.endTime = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Unknown Data Logs
      .addCase(fetchUnknownDataLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchUnknownDataLogs.fulfilled,
        (state, action) => {
          state.loading = false;
          state.unknownDataLogs = action.payload.data || [];
          state.pagination = {
            timestamp: action.payload.meta?.timestamp || "",
            local_time: action.payload.meta?.local_time || "",
            total: action.payload.meta?.total || 0,
            page: action.payload.meta?.page || 1,
            limit: action.payload.meta?.limit || 10,
            total_pages: action.payload.meta?.total_pages || 0,
          };
        }
      )
      .addCase(
        fetchUnknownDataLogs.rejected,
        (state, action) => {
          state.loading = false;
          state.error =
            action.error.message ||
            "Failed to fetch unknown data logs";
        }
      );
  },
});

export const {
  setFilter,
  setPage,
  setLimit,
  setOrderBy,
  clearFilters,
  setStartDate,
  setEndDate,
  setStartTime,
  setEndTime,
} = unknownDataLogAdminSlice.actions;

export default unknownDataLogAdminSlice;
