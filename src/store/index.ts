import {configureStore} from "@reduxjs/toolkit";
import authSlice from "./slices/auth.slice.ts";
import branchAdminSlice from "./slices/admin/branch.admin.slice.ts";
import selectBranchAdminSlice from "./slices/admin/selectBranch.admin.slice.ts";
import branchListSysadminSlice from "./slices/sysadmin/branchList.sysadmin.slice.ts";
import beaconListSysadminSlice from "./slices/sysadmin/beaconList.sysadmin.slice.ts";
import addBeaconSysadminSlice from "./slices/sysadmin/addBeacon.sysadmin.slice.ts";
import licenseListSysadminSlice from "./slices/sysadmin/licenseList.sysadmin.slice.ts";
import addLicenseSysadminSlice from "./slices/sysadmin/addLicense.sysadmin.slice.ts";
import addSubBranchAdminSlice from "./slices/admin/addSubBranch.admin.slice.ts";
import viewBranchAdminSlice from "./slices/admin/viewBranch.admin.slice.ts";
import rolesAdminSlice from "./slices/admin/roles.admin.slice.ts";
import viewRoleAdminSlice from "./slices/admin/viewRole.admin.slice.ts";
import addRoleAdminSlice from "./slices/admin/addRole.admin.slice";
import userAdminSlice from "./slices/admin/user.admin.slice";
import addUserAdminSlice from "./slices/admin/addUser.admin.slice";
import labelAdminSlice from "./slices/admin/label.admin.slice";
import addLabelAdminSlice from "./slices/admin/addLabel.admin.slice";
import activityAdminSlice from "./slices/admin/activity.admin.slice";
import addActivityAdminSlice from "./slices/admin/addActivity.admin.slice.ts";
import taskAdminSlice from "./slices/admin/task.admin.slice";
import addTaskAdminSlice from "./slices/admin/addTask.admin.slice";
import checkpointAdminSlice from "./slices/admin/checkpoint.admin.slice";
import addCheckpointAdminSlice from "./slices/admin/addCheckpoint.admin.slice";
import siteAdminSlice from "./slices/admin/site.admin.slice";
import addSiteAdminSlice from "./slices/admin/addSite.admin.slice";
import geofenceAdminSlice from "./slices/admin/geofence.admin.slice";
import addEditGeofenceAdminSlice from "./slices/admin/addEditGeofence.admin.slice";
import deviceAdminSlice from "./slices/admin/device.admin.slice";
import deviceModalSlice from "./slices/admin/deviceModal.admin.slice";
import updateGpsTrackingIntervalAdmin from "./slices/admin/updateGpsTrackingInterval.admin.slice";
import beaconAdminSlice from "./slices/admin/beacon.admin.slice";
import formAdminSlice from "./slices/admin/form.admin.slice";
import addFormAdminSlice from "./slices/admin/addForm.admin.slice";
import formPicklistAdminSlice from "./slices/admin/formPicklist.admin.slice";
import addFormPicklistAdminSlice from "./slices/admin/addFormPicklist.admin.slice";
import addAlertAdminSlice from "./slices/admin/addAlert.admin.slice.ts";
import alertAdminSlice from "./slices/admin/alert.admin.slice.ts";
import schedulerAdminSlice from "./slices/admin/scheduler.admin.slice";
import addSchedulerAdminSlice from "./slices/admin/addScheduler.admin.slice";
import licenseAdminSlice from "./slices/admin/license.admin.slice";
import activityLogAdminSlice from "./slices/admin/activityLog.admin.slice";
import alarmLogAdminSlice from "./slices/admin/alarmLog.admin.slice";
import averageRotationLogAdminSlice from "./slices/admin/averageRotationLog.admin.slice";
import taskLogAdminSlice from "./slices/admin/taskLog.admin.slice";
import checkpointLogAdminSlice from "./slices/admin/checkpointLog.admin.slice";
import formLogAdminSlice from "./slices/admin/formLog.admin.slice";
import geofenceLogAdminSlice from "./slices/admin/geofenceLog.admin.slice";
import missedZoneAdminSlice from "./slices/admin/missedZone.admin.slice";
import signInOutLogAdminSlice from "./slices/admin/signInOutLog.admin.slice";
import branchDetailsLogAdminSlice from "./slices/admin/branchDetailsLog.admin.slice.ts";
import timeRotationAdminSlice from "./slices/admin/timeRotation.admin.slice";
import timeOnZoneAdminSlice from "./slices/admin/timeOnZone.admin.slice.ts";
import dashboardMapAdminSlice from "./slices/admin/dashboardMap.admin.slice.ts";
import exceptionLogAdminSlice from "./slices/admin/exceptionLog.admin.slice";
import batteryLevelLogAdminSlice from "./slices/admin/batteryLevelLog.admin.slice";
import exceptionDetailedLogAdminSlice from "./slices/admin/exceptionDetailedLog.admin.slice";
import settingListSysadminSlice from "./slices/sysadmin/settingList.sysadmin.slice";
import EditSettingListSysadminSlice from "./slices/sysadmin/EditSetting.sysadmin.slice";
import unknownDataLogAdminSlice from "./slices/admin/unknownDataLog.admin.slice.ts";

export const store = configureStore({
  reducer: {
    // Auth
    auth: authSlice.reducer,

    // Sysadmin
    branchSysadmin: branchListSysadminSlice.reducer,
    beaconSysadmin: beaconListSysadminSlice.reducer,
    addBeaconSysadmin: addBeaconSysadminSlice.reducer,
    licenseSysadmin: licenseListSysadminSlice.reducer,
    addLicenseSysadmin: addLicenseSysadminSlice.reducer,
    selectBranchAdmin: selectBranchAdminSlice.reducer,
    settingSysadmin: settingListSysadminSlice.reducer,
    editSettingSysadmin: EditSettingListSysadminSlice.reducer,

    // Branch Admin
    branchAdmin: branchAdminSlice.reducer,
    modalAddSubBranchAdmin: addSubBranchAdminSlice.reducer,
    viewBranchAdmin: viewBranchAdminSlice.reducer,

    // Roles Admin
    rolesAdmin: rolesAdminSlice.reducer,
    viewRoleAdmin: viewRoleAdminSlice.reducer,
    addRoleAdmin: addRoleAdminSlice.reducer,

    // User Admin
    userAdmin: userAdminSlice.reducer,
    addUserAdmin: addUserAdminSlice.reducer,

    // Label Admin
    labelAdmin: labelAdminSlice.reducer,
    addLabelAdmin: addLabelAdminSlice.reducer,

    // Activity Admin
    activityAdmin: activityAdminSlice.reducer,
    addActivityAdmin: addActivityAdminSlice.reducer,

    // Task Admin
    taskAdmin: taskAdminSlice.reducer,
    addTaskAdmin: addTaskAdminSlice.reducer,

    // Checkpoint Admin
    checkpointAdmin: checkpointAdminSlice.reducer,
    addCheckpointAdmin: addCheckpointAdminSlice.reducer,

    // Site Admin
    siteAdmin: siteAdminSlice.reducer,
    addSiteAdmin: addSiteAdminSlice.reducer,

    // Geofence Admin
    geofenceAdmin: geofenceAdminSlice.reducer,
    addEditGeofenceAdmin: addEditGeofenceAdminSlice.reducer,

    // Device Admin
    deviceAdmin: deviceAdminSlice.reducer,
    deviceModal: deviceModalSlice.reducer,
    updateGpsTrackingIntervalAdmin: updateGpsTrackingIntervalAdmin.reducer,

    // Beacon Admin
    beaconAdmin: beaconAdminSlice.reducer,

    // Form Admin
    formAdmin: formAdminSlice.reducer,
    addFormAdmin: addFormAdminSlice.reducer,

    // Form Picklist
    formPicklistAdmin: formPicklistAdminSlice.reducer,
    addFormPicklistAdmin: addFormPicklistAdminSlice.reducer,

    // Alert Admin
    alertAdmin: alertAdminSlice.reducer,
    addAlertAdmin: addAlertAdminSlice.reducer,

    // Scheduler Admin
    schedulerAdmin: schedulerAdminSlice.reducer,
    addSchedulerAdmin: addSchedulerAdminSlice.reducer,

    // License Admin
    licenseAdmin: licenseAdminSlice.reducer,

    // Activity Log Admin
    activityLogAdmin: activityLogAdminSlice.reducer,

    // Alarm Log Admin
    alarmLogAdmin: alarmLogAdminSlice.reducer,

    // Average Rotation Log Admin
    averageRotationLogAdmin:
    averageRotationLogAdminSlice.reducer,

    // Task Log Admin
    taskLogAdmin: taskLogAdminSlice.reducer,

    // Checkpoint Log Admin
    checkpointLogAdmin: checkpointLogAdminSlice.reducer,

    // Form Log Admin
    formLogAdmin: formLogAdminSlice.reducer,

    // Geofence Log Admin
    geofenceLogAdmin: geofenceLogAdminSlice.reducer,

    // Missed Zone Log Admin
    missedZoneAdmin: missedZoneAdminSlice.reducer,

    // Sign In/Out Log Admin
    signInOutLogAdmin: signInOutLogAdminSlice.reducer,

    // Branch Details Log Admin
    branchDetailsLogAdmin: branchDetailsLogAdminSlice.reducer,

    // Time Rotation Admin
    timeRotationAdmin: timeRotationAdminSlice.reducer,

    // Time On Zone Admin
    timeOnZoneAdmin: timeOnZoneAdminSlice.reducer,

    // Dashboard Map Admin
    dashboardMapAdmin: dashboardMapAdminSlice.reducer,

    // Exception Log Admin
    exceptionLogAdmin: exceptionLogAdminSlice.reducer,

    // Exception Detailed Log Admin
    exceptionDetailedLogAdmin: exceptionDetailedLogAdminSlice.reducer,

    // Battery Level Log Admin
    batteryLevelLogAdmin: batteryLevelLogAdminSlice.reducer,

    // Unknown Data Admin
    unknownDataAdmin: unknownDataLogAdminSlice.reducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
