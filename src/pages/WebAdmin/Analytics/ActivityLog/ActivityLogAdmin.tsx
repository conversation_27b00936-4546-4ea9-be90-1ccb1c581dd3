import { useTranslation } from "react-i18next";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>d<PERSON><PERSON><PERSON> } from "react-icons/md";
import { useEffect, useState, useRef } from "react";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import ActiveFilters, {
  createEndDateFilter,
  createEndTimeFilter,
  createLabelFilter,
  createSimpleFilter,
  createStartDateFilter,
  createStartTimeFilter,
  FilterGroup,
} from "../../../../components/Admin/Common/ActiveFilters";
import { <PERSON><PERSON>, Drawer } from "antd";
import AnalyticDrawerForm, {
  AnalyticsFilterState,
} from "../../Analytics/Components/AnalyticDrawerForm";
import { EReportType } from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import { AdminRole } from "../../../../services/mainApi/admin/types/roles.admin.mainApi.types";
import { AdminUser } from "../../../../services/mainApi/admin/types/user.admin.mainApi.types";
import { AdminDevice } from "../../../../services/mainApi/admin/types/device.admin.mainApi.types";
import { AdminActivity } from "../../../../services/mainApi/admin/types/activity.admin.mainApi.types";
import { AdminLabel } from "../../../../services/mainApi/admin/types/label.admin.mainApi.types";
import { AdminBranch } from "../../../../services/mainApi/admin/types/branch.admin.mainApi.types";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../store/hooks";
import ActivityLogList from "./Components/ActivityLogList";
import activityLogAdminSlice, {
  fetchActivityLogs,
} from "../../../../store/slices/admin/activityLog.admin.slice";
import { useParams } from "react-router-dom";
import ActivityLogExportButton from "./Components/ActivityLogExportButton";

interface FilterState {
  role: null | AdminRole;
  user: null | AdminUser;
  userLabels: AdminLabel[];
  device: null | AdminDevice;
  deviceLabels: AdminLabel[];
  activity: null | AdminActivity;
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  selectedBranch: AdminBranch | null;
}

const ActivityLogAdmin = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const isFetchingRef = useRef(false);

  // Get branch code from URL params
  const { branchCode } = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const activityLogState = useAppSelector(
    (state) => state.activityLogAdmin
  );
  const loading = activityLogState.loading;
  const pagination = activityLogState.pagination;
  const activityLogs = activityLogState.activityLogs;

  const selectedRole = activityLogState.selectedRole;
  const selectedUser = activityLogState.selectedUser;
  const selectedDevice = activityLogState.selectedDevice;
  const selectedActivity =
    activityLogState.selectedActivity;
  const selectedBranch = activityLogState.selectedBranch;
  const selectedUserLabels =
    activityLogState.selectedUserLabels;
  const selectedDeviceLabels =
    activityLogState.selectedDeviceLabels;
  const filterState = activityLogState.filter;

  // Load data on mount and when filters change
  useEffect(() => {
    if (branchCode && !isFetchingRef.current) {
      isFetchingRef.current = true;
      dispatch(fetchActivityLogs({ branchCode }))
        .finally(() => {
          isFetchingRef.current = false;
        });
    }
  }, [
    dispatch,
    branchCode,
    filterState.page,
    filterState.limit,
    filterState.startDate,
    filterState.endDate,
    filterState.startTime,
    filterState.endTime,
    filterState.branchId,
    filterState.roleId,
    filterState.userId,
    filterState.userLabels,
    filterState.deviceId,
    filterState.deviceLabels,
    filterState.activityId,
    filterState.orderBy,
    filterState.orderDirection,
  ]);

  const { actions } = activityLogAdminSlice;

  // Convert applied filter state to format expected by drawer
  const getAppliedFilters = (): FilterState => {
    return {
      role: selectedRole,
      user: selectedUser,
      userLabels: selectedUserLabels || [],
      device: selectedDevice,
      deviceLabels: selectedDeviceLabels || [],
      activity: selectedActivity,
      startDate: filterState.startDate,
      endDate: filterState.endDate,
      startTime: filterState.startTime,
      endTime: filterState.endTime,
      selectedBranch: selectedBranch,
    };
  };

  // Apply filters from the drawer
  const handleApplyFilters = (
    newFilters: AnalyticsFilterState
  ) => {
    // Update redux state
    dispatch(
      actions.setSelectedRole(newFilters.role || null)
    );
    dispatch(
      actions.setSelectedUser(newFilters.user || null)
    );
    dispatch(
      actions.setSelectedUserLabels(
        newFilters.userLabels || []
      )
    );
    dispatch(
      actions.setSelectedDevice(newFilters.device || null)
    );
    dispatch(
      actions.setSelectedDeviceLabels(
        newFilters.deviceLabels || []
      )
    );
    dispatch(
      actions.setSelectedActivity(
        newFilters.activity || null
      )
    );
    dispatch(
      actions.setSelectedBranch(
        newFilters.selectedBranch || null
      )
    );
    dispatch(
      actions.setStartDate(newFilters.startDate || null)
    );
    dispatch(
      actions.setEndDate(newFilters.endDate || null)
    );
    dispatch(
      actions.setStartTime(newFilters.startTime || null)
    );
    dispatch(
      actions.setEndTime(newFilters.endTime || null)
    );

    // Reset page to 1
    dispatch(actions.setPage(1));

    setDrawerVisible(false);
  };

  // Handle page change
  const handlePageChange = (
    page: number,
    pageSize?: number
  ) => {
    if (pageSize && pageSize !== filterState.limit) {
      dispatch(actions.setLimit(pageSize));
    }
    dispatch(actions.setPage(page));
  };

  // Check if there are any active filters
  const hasActiveFilters = (): boolean => {
    // Check if any filter is active
    return !!(filterState.startDate ||
      filterState.endDate ||
      filterState.startTime ||
      filterState.endTime ||
      filterState.branchId ||
      filterState.roleId ||
      filterState.userId ||
      filterState.userLabels.length > 0 ||
      filterState.deviceId ||
      filterState.deviceLabels.length > 0 ||
      filterState.activityId);


  };

  // Get filter groups configuration for ActiveFilters component
  const getFilterGroups = (): FilterGroup[] => {
    const appliedFilters = getAppliedFilters();

    return [
      {
        key: "datetime",
        filters: [
          createStartDateFilter(appliedFilters.startDate),
          createEndDateFilter(appliedFilters.endDate),
          createStartTimeFilter(appliedFilters.startTime),
          createEndTimeFilter(appliedFilters.endTime),
        ],
      },
      {
        key: "user",
        filters: [
          createSimpleFilter(
            "role",
            "role",
            appliedFilters.role,
            "role_name"
          ),
          createSimpleFilter(
            "user",
            "user",
            appliedFilters.user,
            "name"
          ),
          createLabelFilter(
            "userLabels",
            "userLabels",
            appliedFilters.userLabels
          ),
        ],
      },
      {
        key: "device",
        filters: [
          createSimpleFilter(
            "device",
            "device",
            appliedFilters.device,
            "device_name"
          ),
          createLabelFilter(
            "deviceLabels",
            "deviceLabels",
            appliedFilters.deviceLabels
          ),
        ],
      },
      {
        key: "other",
        filters: [
          createSimpleFilter(
            "activity",
            "activity",
            appliedFilters.activity,
            "activity_name"
          ),
          createSimpleFilter(
            "selectedBranch",
            "selectedBranch",
            appliedFilters.selectedBranch,
            "branch_name"
          ),
        ],
      },
    ];
  };

  const handleClearAll = () => {
    dispatch(actions.clearFilters());
  };

  const handleRemoveFilter = (
    filterKey: keyof FilterState
  ) => {
    switch (filterKey) {
      case "role":
        dispatch(actions.setSelectedRole(null));
        break;
      case "user":
        dispatch(actions.setSelectedUser(null));
        break;
      case "userLabels":
        dispatch(actions.setSelectedUserLabels([]));
        break;
      case "device":
        dispatch(actions.setSelectedDevice(null));
        break;
      case "deviceLabels":
        dispatch(actions.setSelectedDeviceLabels([]));
        break;
      case "activity":
        dispatch(actions.setSelectedActivity(null));
        break;
      case "selectedBranch":
        dispatch(actions.setSelectedBranch(null));
        break;
      case "startDate":
        dispatch(actions.setStartDate(null));
        break;
      case "endDate":
        dispatch(actions.setEndDate(null));
        break;
      case "startTime":
        dispatch(actions.setStartTime(null));
        break;
      case "endTime":
        dispatch(actions.setEndTime(null));
        break;
    }
  };

  // Handle clicking on an active filter to edit it
  const handleEditFilter = () => {
    // We only need to open the drawer
    setDrawerVisible(true);
  };

  // Convert FilterState to AnalyticsFilterState for the drawer form
  const getInitialDrawerValues =
    (): Partial<AnalyticsFilterState> => {
      const appliedFilters = getAppliedFilters();

      return {
        startDate: appliedFilters.startDate || "",
        endDate: appliedFilters.endDate || "",
        startTime: appliedFilters.startTime || "",
        endTime: appliedFilters.endTime || "",
        selectedBranch:
          appliedFilters.selectedBranch || null,
        role: appliedFilters.role,
        user: appliedFilters.user,
        userLabels: appliedFilters.userLabels,
        device: appliedFilters.device,
        deviceLabels: appliedFilters.deviceLabels,
        activity: appliedFilters.activity,
      };
    };

  // Create a reusable function to get filter info
  const getFilterInfo = (): {
    role: AdminRole | null;
    user: AdminUser | null;
    userLabels: AdminLabel[];
    device: AdminDevice | null;
    deviceLabels: AdminLabel[];
    activity: AdminActivity | null;
    startDate: string | null;
    endDate: string | null;
    startTime: string | null;
    endTime: string | null;
    selectedBranch: AdminBranch | null;
  } => ({
    role: activityLogState.selectedRole || null,
    user: activityLogState.selectedUser || null,
    userLabels: activityLogState.selectedUserLabels || [],
    device: activityLogState.selectedDevice || null,
    deviceLabels:
      activityLogState.selectedDeviceLabels || [],
    activity: activityLogState.selectedActivity || null,
    startDate: activityLogState.filter.startDate || null,
    endDate: activityLogState.filter.endDate || null,
    startTime: activityLogState.filter.startTime || null,
    endTime: activityLogState.filter.endTime || null,
    selectedBranch: activityLogState.selectedBranch || null,
  });

  return (
    <WebAdminLayout activePage="activity-log">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdHistory />}
            title={t("activityLog.title", "Activity Log")}
            description={t(
              "activityLog.description",
              "View and manage activity logs"
            )}
            actions={[
              // Desktop view - show all buttons
              <div
                key="desktop-actions"
                className="hidden md:flex"
              >
                <ActivityLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                />
                <Button
                  key="filter"
                  icon={<MdFilterList />}
                  size="large"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,

              // Mobile view - show filter button and dropdown for downloads
              <div
                key="mobile-actions"
                className="flex md:hidden"
              >
                <ActivityLogExportButton
                  filters={getFilterInfo()}
                  className="mr-2"
                  buttonText={t(
                    "common.download",
                    "Download"
                  )}
                />
                <Button
                  key="filter-mobile"
                  icon={<MdFilterList />}
                  size="large"
                  type="primary"
                  className="bg-indigo-600 hover:bg-indigo-700 border-0 shadow-md hover:shadow-lg transition-all duration-300 flex items-center"
                  onClick={() => {
                    setDrawerVisible(true);
                  }}
                >
                  {t("common.filters", "Filters")}
                </Button>
              </div>,
            ]}
          />

          {/* Active Filters using the reusable component */}
          <ActiveFilters<FilterState>
            filterGroups={getFilterGroups()}
            onRemoveFilter={handleRemoveFilter}
            onEditFilter={handleEditFilter}
            onClearAll={handleClearAll}
            onShowFilters={() => {
              setDrawerVisible(true);
            }}
            hasActiveFilters={hasActiveFilters}
          />

          {/* Results List */}
          <ActivityLogList
            activityLogs={activityLogs}
            loading={loading}
            total={pagination.total || 0}
            page={pagination.page || 1}
            limit={pagination.limit || 10}
            onPageChange={handlePageChange}
          />
        </div>

        {/* Filter Drawer with modern styling */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t(
                "activityLog.filters.title",
                "Filter Activity Log"
              )}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.ACTIVITY_LOG}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default ActivityLogAdmin;
