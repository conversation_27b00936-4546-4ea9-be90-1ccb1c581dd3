import React from "react";
import { <PERSON>, Tooltip } from "antd";
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>ser, FiCalendar, FiServer, FiMapPin} from "react-icons/fi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { useTranslation } from "react-i18next";

dayjs.extend(relativeTime);

import { AdminUnknownDataEntry } from "../../../../../services/mainApi/admin/types/unknownData.admin.mainApi.types";

// Interface for the unknown data item props
interface UnknownDataItemProps {
  unknownDataLog: AdminUnknownDataEntry;
}

const UnknownDataItem: React.FC<UnknownDataItemProps> = ({ unknownDataLog }) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-4">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-base font-medium text-gray-900 dark:text-white">
            {
              unknownDataLog.checkpoint_device_name
            }
          </h3>
          <div className="flex gap-2 items-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              ID: {unknownDataLog.id}
            </p>
            <Tag color="blue">
              Unknown Data
            </Tag>
          </div>
        </div>
        <Tooltip
          title={dayjs(unknownDataLog.event_time).format(
            "DD MMM YYYY HH:mm:ss"
          )}
        >
          <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
            <FiClock className="inline" />
            {dayjs(unknownDataLog.event_time).fromNow()}
          </span>
        </Tooltip>
      </div>

      {/* Device Information */}
      <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg mb-4">
        <div className="flex items-center gap-2 mb-2">
          <FiServer className="text-blue-500" />
          <span className="font-medium">
            {t("unknownData.deviceInfo", "Device Information")}
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              <span className="font-medium">{t("unknownData.device", "Device")}:</span> {unknownDataLog.device_name}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              <span className="font-medium">{t("unknownData.deviceId", "Device ID")}:</span> {unknownDataLog.device_id}
            </p>
          </div>
          {unknownDataLog.serial_number && (
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">{t("unknownData.serialNumber", "Serial Number")}:</span> {unknownDataLog.serial_number}
              </p>
            </div>
          )}
          <div>
            <p className="text-sm text-gray-700 dark:text-gray-300">
              <span className="font-medium">{t("unknownData.checkpointType", "Checkpoint Type")}:</span> {unknownDataLog.checkpoint_type_id}
            </p>
          </div>
        </div>
      </div>

      {/* Location Information */}
      <div className="mb-4">
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <FiMapPin className="text-blue-500" />
            <span className="font-medium text-sm">
              {t("unknownData.locationInfo", "Location Information")}
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">{t("unknownData.latitude", "Latitude")}:</span> {unknownDataLog.latitude}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">{t("unknownData.longitude", "Longitude")}:</span> {unknownDataLog.longitude}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">{t("unknownData.majorValue", "Major Value")}:</span> {unknownDataLog.major_value}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <span className="font-medium">{t("unknownData.minorValue", "Minor Value")}:</span> {unknownDataLog.minor_value}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Left Column */}
        <div className="space-y-3">
          {/* User Info */}
          <div className="flex items-center gap-2 text-sm">
            <FiUser className="text-green-500" />
            <span className="text-gray-600 dark:text-gray-300">
              {t("unknownData.user", "User")}:
              {" "}{unknownDataLog.user_name}
              {unknownDataLog.role_name && ` (${unknownDataLog.role_name})`}
            </span>
          </div>

          {/* Branch Info */}
          <div className="flex items-center gap-2 text-sm">
            <FiServer className="text-blue-500" />
            <span className="text-gray-600 dark:text-gray-300">
              {t("unknownData.branch", "Branch")}:
              {" "}{unknownDataLog.parent_branch_id}
            </span>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-3">
          {/* Event Time */}
          <div className="flex items-center gap-2 text-sm">
            <FiCalendar className="text-purple-500" />
            <span className="text-gray-600 dark:text-gray-300">
              {t("unknownData.eventTime", "Event Time")}:
              {" "}{dayjs(unknownDataLog.event_time).format("DD MMM YYYY HH:mm:ss")}
            </span>
          </div>

          {/* Submitted Time */}
          <div className="flex items-center gap-2 text-sm">
            <FiClock className="text-orange-500" />
            <span className="text-gray-600 dark:text-gray-300">
              {t("unknownData.submittedTime", "Submitted")}:
              {" "}{dayjs(unknownDataLog.original_submitted_time).format("DD MMM YYYY HH:mm:ss")}
            </span>
          </div>
        </div>
      </div>

      {/* Timezone Information */}
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-1">
          <FiClock className="text-blue-500" />
          <span className="font-medium text-sm">
            {t("unknownData.timezoneInfo", "Timezone Information")}
          </span>
        </div>
        <p className="text-sm text-gray-700 dark:text-gray-300 pl-6">
          {unknownDataLog.timezone_name} (ID: {unknownDataLog.timezone_id})
        </p>
      </div>

      {/* Footer Section */}
      <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <span>
            {t("unknownData.uuid", "UUID")}: {unknownDataLog.uuid}
          </span>
        </div>
      </div>
    </div>
  );
};

export default UnknownDataItem;
