import React from "react";
import { Empty, Pagination, Spin } from "antd";
import { useTranslation } from "react-i18next";
import UnknownDataItem from "./UnknownDataItem";
import { AdminUnknownDataEntry } from "../../../../../services/mainApi/admin/types/unknownData.admin.mainApi.types";

// Interface for the unknown data list props
interface UnknownDataListProps {
  unknownDataLogs: AdminUnknownDataEntry[];
  loading: boolean;
  total: number;
  page: number;
  limit: number;
  onPageChange: (page: number, pageSize?: number) => void;
}

const UnknownDataList: React.FC<UnknownDataListProps> = ({
  unknownDataLogs,
  loading,
  total,
  page,
  limit,
  onPageChange,
}) => {
  const { t } = useTranslation();

  if (loading) {
    return (
      <div className="flex justify-center items-center py-16">
        <Spin size="large" />
      </div>
    );
  }

  if (unknownDataLogs.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-12">
        <Empty
          description={t(
            "unknownData.noLogs",
            "No unknown data logs found"
          )}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {unknownDataLogs.map((log) => (
        <UnknownDataItem
          key={log.id}
          unknownDataLog={log}
        />
      ))}

      {/* Pagination */}
      {total > 0 && (
        <div className="flex justify-end my-6">
          <Pagination
            current={page}
            pageSize={limit}
            total={total}
            onChange={onPageChange}
            showSizeChanger
            pageSizeOptions={["10", "20", "50", "100"]}
            className="dark:text-gray-300"
          />
        </div>
      )}
    </div>
  );
};

export default UnknownDataList;
