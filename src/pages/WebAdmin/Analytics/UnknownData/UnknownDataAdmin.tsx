import {useTranslation} from "react-i18next";
import {<PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON>, MdHelpOutline} from "react-icons/md";
import {useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import {<PERSON><PERSON>, Drawer} from "antd";
import WebAdminLayout from "../../../../components/Layout/WebAdminLayout";
import PageHeader from "../../../../components/Admin/Common/PageHeader";
import UnknownDataList from "./Components/UnknownDataList";
import AnalyticDrawerForm, {AnalyticsFilterState} from "../Components/AnalyticDrawerForm";
import {EReportType} from "../../../../services/mainApi/admin/types/scheduler.admin.mainApi.types";
import {AppDispatch, RootState} from "../../../../store";
import {
  fetchUnknownDataLogs,
  setEndDate,
  setEndTime,
  setLimit,
  setPage,
  setStartDate,
  setStartTime,
} from "../../../../store/slices/admin/unknownDataLog.admin.slice";
import {useParams} from "react-router-dom";

const UnknownDataAdmin = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch<AppDispatch>();

  // Get branch code from URL params
  const {branchCode} = useParams<{
    branchCode: string;
  }>();

  // Redux state
  const {
    unknownDataLogs,
    loading,
    pagination,
    filter,
  } = useSelector((state: RootState) => state.unknownDataAdmin);

  // Local state for drawer
  const [drawerVisible, setDrawerVisible] = useState(false);

  // Fetch data when component mounts or filters change
  useEffect(() => {
    if (branchCode) {
      dispatch(fetchUnknownDataLogs({branchCode: branchCode}));
    }
  }, [dispatch, branchCode, filter]);

  // Handle page change
  const handlePageChange = (page: number, pageSize?: number) => {
    dispatch(setPage(page));
    if (pageSize) {
      dispatch(setLimit(pageSize));
    }
  };

  // Apply filters from the drawer
  const handleApplyFilters = (newFilters: AnalyticsFilterState) => {
    // Update redux state with filter values
    dispatch(setStartDate(newFilters.startDate || null));
    dispatch(setEndDate(newFilters.endDate || null));
    dispatch(setStartTime(newFilters.startTime || null));
    dispatch(setEndTime(newFilters.endTime || null));

    // Close drawer
    setDrawerVisible(false);
  };

  // Get initial values for the drawer form based on current filter state
  const getInitialDrawerValues = (): Partial<AnalyticsFilterState> => {
    return {
      startDate: filter.startDate || "",
      endDate: filter.endDate || "",
      startTime: filter.startTime || "",
      endTime: filter.endTime || "",
    };
  };

  return (
    <WebAdminLayout activePage="unknown-data">
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <PageHeader
            icon={<MdHelpOutline/>}
            title={t(
              "unknownData.title",
              "Unknown Data"
            )}
            description={t(
              "unknownData.description",
              "View and analyze unrecognized or corrupted data for troubleshooting"
            )}
            actions={[
              <Button
                key="filter"
                icon={<MdFilterList/>}
                size="large"
                onClick={() => setDrawerVisible(true)}
              >
                {t("common.filters", "Filters")}
              </Button>,
            ]}
          />

          {/* Unknown Data List */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">
              {t("unknownData.dataList", "Unknown Data List")}
            </h2>

            <UnknownDataList
              unknownDataLogs={unknownDataLogs}
              loading={loading}
              total={pagination.total}
              page={pagination.page}
              limit={pagination.limit}
              onPageChange={handlePageChange}
            />
          </div>
        </div>

        {/* Filter Drawer */}
        <Drawer
          title={
            <div className="text-lg font-semibold text-gray-800 dark:text-white">
              {t("unknownData.filters.title", "Filter Unknown Data")}
            </div>
          }
          placement="right"
          onClose={() => setDrawerVisible(false)}
          open={drawerVisible}
          width={400}
          className="dark:bg-gray-800"
        >
          <AnalyticDrawerForm
            reportType={EReportType.UNKNOWN}
            onApply={handleApplyFilters}
            onClose={() => setDrawerVisible(false)}
            initialValues={getInitialDrawerValues()}
          />
        </Drawer>
      </div>
    </WebAdminLayout>
  );
};

export default UnknownDataAdmin;
