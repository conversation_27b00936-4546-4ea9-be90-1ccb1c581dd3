import baseMainApi from "../base.mainApi";
import {
  AdminUnknownDataListParams,
  AdminUnknownDataListResponse,
} from "./types/unknownData.admin.mainApi.types";

export const unknownDataAdminApi = {
  getUnknownData: async (
    branchCode: string,
    params?: AdminUnknownDataListParams
  ) => {
    const response =
      await baseMainApi.get<AdminUnknownDataListResponse>(
        `/web-api/admin/${branchCode}/unknown-data`,
        { params }
      );
    return response.data;
  },
};
